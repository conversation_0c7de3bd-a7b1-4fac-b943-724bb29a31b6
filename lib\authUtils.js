import jwt from 'jsonwebtoken';

// Hardcoded admin credentials (in production, use environment variables)
const ADMIN_CREDENTIALS = {
  username: process.env.ADMIN_USERNAME,
  password: process.env.ADMIN_PASSWORD
};

const JWT_SECRET = process.env.JWT_SECRET || 'caf-admin-secret-key-2024';

/**
 * Validate admin credentials
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {boolean} - True if credentials are valid
 */
export const validateAdminCredentials = (username, password) => {
  return username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password;
};

/**
 * Generate JWT token for admin
 * @param {string} username - Username
 * @returns {string} - JWT token
 */
export const generateAdminToken = (username) => {
  return jwt.sign(
    {
      username,
      role: 'admin',
      iat: Math.floor(Date.now() / 1000)
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
};

/**
 * Verify JWT token
 * @param {string} token - JWT token
 * @returns {Object|null} - Decoded token or null if invalid
 */
export const verifyAdminToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
};

/**
 * Extract token from request headers
 * @param {Request} request - Next.js request object
 * @returns {string|null} - Token or null
 */
export const extractTokenFromRequest = (request) => {
  const authHeader = request.headers.get('authorization');

  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
};

/**
 * Middleware to check admin authentication
 * @param {Request} request - Next.js request object
 * @returns {Object|null} - User data or null if not authenticated
 */
export const authenticateAdmin = (request) => {
  const token = extractTokenFromRequest(request);

  if (!token) {
    return null;
  }

  const decoded = verifyAdminToken(token);

  if (!decoded || decoded.role !== 'admin') {
    return null;
  }

  return decoded;
};

/**
 * Get admin credentials for display (for development/testing)
 * @returns {Object} - Admin credentials
 */
export const getAdminCredentials = () => {
  return {
    username: ADMIN_CREDENTIALS.username,
    password: ADMIN_CREDENTIALS.password
  };
};