import { supabase, TABLES, handleSupabaseError } from './supabase.js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'caf-admin-secret-key-2024';

/**
 * Validate admin credentials against Supabase
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Object|null} User data if valid, null if invalid
 */
export const validateAdminCredentials = async (username, password) => {
  try {
    // First, try to get user from Supabase
    const { data: user, error } = await supabase
      .from(TABLES.ADMIN_USERS)
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      // Fallback to environment variables for backward compatibility
      const envUsername = process.env.ADMIN_USERNAME;
      const envPassword = process.env.ADMIN_PASSWORD;
      
      if (username === envUsername && password === envPassword) {
        return {
          id: 'env-admin',
          username: envUsername,
          email: '<EMAIL>',
          role: 'admin'
        };
      }
      
      return null;
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    
    if (!isPasswordValid) {
      return null;
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };
  } catch (error) {
    console.error('Error validating admin credentials:', error);
    return null;
  }
};

/**
 * Generate JWT token for admin
 * @param {Object} user - User data
 * @returns {string} JWT token
 */
export const generateAdminToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      iat: Math.floor(Date.now() / 1000)
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
};

/**
 * Verify JWT token
 * @param {string} token - JWT token
 * @returns {Object|null} Decoded token or null if invalid
 */
export const verifyAdminToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
};

/**
 * Extract token from request headers
 * @param {Request} request - Next.js request object
 * @returns {string|null} Token or null if not found
 */
export const extractTokenFromRequest = (request) => {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(' ');
  
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1];
};

/**
 * Middleware to check admin authentication
 * @param {Request} request - Next.js request object
 * @returns {Object|null} User data or null if not authenticated
 */
export const authenticateAdmin = (request) => {
  const token = extractTokenFromRequest(request);

  if (!token) {
    return null;
  }

  const decoded = verifyAdminToken(token);

  if (!decoded || decoded.role !== 'admin') {
    return null;
  }

  return decoded;
};

/**
 * Create a new admin user in Supabase
 * @param {Object} userData - User data
 * @returns {Object} Created user data
 */
export const createAdminUser = async (userData) => {
  try {
    const { username, email, password, role = 'admin' } = userData;

    // Hash password
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(password, saltRounds);

    const { data, error } = await supabase
      .from(TABLES.ADMIN_USERS)
      .insert([{
        username,
        email,
        password_hash,
        role,
        is_active: true
      }])
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'create admin user');
    }

    // Return user data without password hash
    return {
      id: data.id,
      username: data.username,
      email: data.email,
      role: data.role,
      is_active: data.is_active,
      created_at: data.created_at
    };
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
};

/**
 * Update admin user password
 * @param {string} userId - User ID
 * @param {string} newPassword - New password
 * @returns {boolean} Success status
 */
export const updateAdminPassword = async (userId, newPassword) => {
  try {
    // Hash new password
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(newPassword, saltRounds);

    const { data, error } = await supabase
      .from(TABLES.ADMIN_USERS)
      .update({ password_hash })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'update admin password');
    }

    return !!data;
  } catch (error) {
    console.error('Error updating admin password:', error);
    return false;
  }
};

/**
 * Get admin user by ID
 * @param {string} userId - User ID
 * @returns {Object|null} User data or null if not found
 */
export const getAdminUser = async (userId) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.ADMIN_USERS)
      .select('id, username, email, role, is_active, created_at, updated_at')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // User not found
      }
      handleSupabaseError(error, 'get admin user');
    }

    return data;
  } catch (error) {
    console.error('Error getting admin user:', error);
    return null;
  }
};

/**
 * Deactivate admin user
 * @param {string} userId - User ID
 * @returns {boolean} Success status
 */
export const deactivateAdminUser = async (userId) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.ADMIN_USERS)
      .update({ is_active: false })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      handleSupabaseError(error, 'deactivate admin user');
    }

    return !!data;
  } catch (error) {
    console.error('Error deactivating admin user:', error);
    return false;
  }
};

/**
 * Get admin credentials for display (for development/testing)
 * This maintains backward compatibility with the existing system
 * @returns {Object} Admin credentials
 */
export const getAdminCredentials = () => {
  return {
    username: process.env.ADMIN_USERNAME,
    password: process.env.ADMIN_PASSWORD
  };
};

/**
 * Initialize default admin user if it doesn't exist
 * This function should be called during application startup
 */
export const initializeDefaultAdmin = async () => {
  try {
    const defaultUsername = process.env.ADMIN_USERNAME || 'admin';
    const defaultPassword = process.env.ADMIN_PASSWORD || 'caf2024!';
    const defaultEmail = '<EMAIL>';

    // Check if admin user already exists
    const { data: existingUser } = await supabase
      .from(TABLES.ADMIN_USERS)
      .select('id')
      .eq('username', defaultUsername)
      .single();

    if (!existingUser) {
      // Create default admin user
      await createAdminUser({
        username: defaultUsername,
        email: defaultEmail,
        password: defaultPassword,
        role: 'admin'
      });
      
      console.log('Default admin user created successfully');
    }
  } catch (error) {
    console.error('Error initializing default admin:', error);
    // Don't throw error to prevent application startup failure
  }
};
