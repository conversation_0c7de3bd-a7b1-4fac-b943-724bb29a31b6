import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { addAppointment, isTimeSlotAvailable } from '../../../lib/supabaseAppointmentUtils.js';
import { isWeekday } from '../../../lib/utils.js';

// Configure your email settings here
const CAF_EMAIL = process.env.CAF_EMAIL || '<EMAIL>';
const EMAIL_USER = process.env.EMAIL_USER || '<EMAIL>';
const EMAIL_PASS = process.env.EMAIL_PASS || 'jpxjlwdlvfkceqgi';

export async function POST(request) {
  try {
    const data = await request.json();
    const { nome, cognome, telefono, email, servizio, prestazione, dataAppuntamento, orario } = data;

    // Validate required fields
    if (!nome || !cognome || !telefono || !email || !servizio || !dataAppuntamento || !orario) {
      return NextResponse.json(
        { success: false, message: 'Tutti i campi sono obbligatori' },
        { status: 400 }
      );
    }

    // Validate that the date is a weekday
    if (!isWeekday(dataAppuntamento)) {
      return NextResponse.json(
        { success: false, message: 'Gli appuntamenti sono disponibili solo dal lunedì al venerdì' },
        { status: 400 }
      );
    }

    // Check if the date is in the past
    const selectedDate = new Date(dataAppuntamento);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return NextResponse.json(
        { success: false, message: 'Non è possibile prenotare appuntamenti per date passate' },
        { status: 400 }
      );
    }

    // Check if the time slot is available
    if (!(await isTimeSlotAvailable(dataAppuntamento, orario))) {
      return NextResponse.json(
        {
          success: false,
          message: 'L\'orario selezionato non è più disponibile. Ricarica la pagina e scegli un altro orario.',
          code: 'TIME_SLOT_UNAVAILABLE'
        },
        { status: 409 } // Conflict status code
      );
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail', // You can change this to your email provider
      auth: {
        user: EMAIL_USER,
        pass: EMAIL_PASS,
      },
    });

    // Format date for display
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleDateString('it-IT', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    // Email content for user
    const userEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #252B59; color: white; padding: 20px; text-align: center;">
          <h1>Conferma Appuntamento CAF</h1>
        </div>
        
        <div style="padding: 20px; background-color: #F7F7F5;">
          <h2 style="color: #B42C2C;">Gentile ${nome} ${cognome},</h2>
          
          <p>Il suo appuntamento è stato confermato con i seguenti dettagli:</p>
          
          <div style="background-color: white; padding: 15px; border-left: 4px solid #B42C2C; margin: 20px 0;">
            <p><strong>Servizio:</strong> ${servizio}</p>
            ${prestazione ? `<p><strong>Prestazione:</strong> ${prestazione}</p>` : ''}
            <p><strong>Data:</strong> ${formatDate(dataAppuntamento)}</p>
            <p><strong>Orario:</strong> ${orario}</p>
            <p><strong>Telefono:</strong> ${telefono}</p>
            <p><strong>Email:</strong> ${email}</p>
          </div>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>Nota importante:</strong> L'orario potrebbe subire lievi variazioni. Se al suo arrivo un altro cliente è già in fase di servizio, verrà chiamato subito dopo.</p>
          </div>
          
          <p>La ringraziamo per aver scelto i nostri servizi.</p>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #D1D1D1;">
            <p style="color: #555555; font-size: 14px;">
              Centro di Assistenza Fiscale<br>
              Per informazioni: ${CAF_EMAIL}
            </p>
          </div>
        </div>
      </div>
    `;

    // Email content for CAF office
    const cafEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #252B59; color: white; padding: 20px; text-align: center;">
          <h1>Nuovo Appuntamento Prenotato</h1>
        </div>
        
        <div style="padding: 20px; background-color: #F7F7F5;">
          <h2 style="color: #B42C2C;">Dettagli Appuntamento</h2>
          
          <div style="background-color: white; padding: 15px; border-left: 4px solid #B42C2C; margin: 20px 0;">
            <p><strong>Cliente:</strong> ${nome} ${cognome}</p>
            <p><strong>Telefono:</strong> ${telefono}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Servizio richiesto:</strong> ${servizio}</p>
            ${prestazione ? `<p><strong>Prestazione:</strong> ${prestazione}</p>` : ''}
            <p><strong>Data:</strong> ${formatDate(dataAppuntamento)}</p>
            <p><strong>Orario:</strong> ${orario}</p>
          </div>
          
          <p>Appuntamento prenotato tramite il sistema online.</p>
        </div>
      </div>
    `;

    // Save appointment to Supabase
    const savedAppointment = await addAppointment(data);

    // Send email to user
    await transporter.sendMail({
      from: EMAIL_USER,
      to: email,
      subject: 'Conferma Appuntamento CAF',
      html: userEmailContent,
    });

    // Send email to CAF office
    await transporter.sendMail({
      from: EMAIL_USER,
      to: CAF_EMAIL,
      subject: `Nuovo Appuntamento - ${nome} ${cognome} - ${formatDate(dataAppuntamento)} ${orario}`,
      html: cafEmailContent,
    });

    return NextResponse.json({
      success: true,
      message: 'Appointment saved and emails sent successfully',
      appointmentId: savedAppointment.id
    });

  } catch (error) {
    console.error('Error sending emails:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send emails' },
      { status: 500 }
    );
  }
}
