-- Migration to add prestazione column to appointments table
-- Run this in your Supabase SQL Editor

-- Add prestazione column to appointments table
ALTER TABLE appointments 
ADD COLUMN prestazione VARCHAR(255);

-- Add comment to document the new column
COMMENT ON COLUMN appointments.prestazione IS 'Specific service type selected by the user (e.g., ISEE, Modello 730, etc.)';

-- Update existing records to have NULL prestazione (they will need to be updated manually if needed)
-- No default value is set as this is a new field and existing appointments don't have this information

-- Optional: Create an index on prestazione for better query performance
CREATE INDEX IF NOT EXISTS idx_appointments_prestazione ON appointments(prestazione);

-- Optional: Create a composite index for service and prestazione queries
CREATE INDEX IF NOT EXISTS idx_appointments_servizio_prestazione ON appointments(servizio, prestazione);
