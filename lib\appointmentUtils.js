import fs from 'fs';
import path from 'path';

const APPOINTMENTS_FILE = path.join(process.cwd(), 'data', 'appointments.json');

/**
 * Read appointments from JSON file
 * @returns {Array} Array of appointments
 */
export const readAppointments = () => {
  try {
    if (!fs.existsSync(APPOINTMENTS_FILE)) {
      // Create the file if it doesn't exist
      fs.writeFileSync(APPOINTMENTS_FILE, '[]');
      return [];
    }

    const data = fs.readFileSync(APPOINTMENTS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading appointments:', error);
    return [];
  }
};

/**
 * Write appointments to JSON file
 * @param {Array} appointments - Array of appointments
 */
export const writeAppointments = (appointments) => {
  try {
    // Ensure data directory exists
    const dataDir = path.dirname(APPOINTMENTS_FILE);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    fs.writeFileSync(APPOINTMENTS_FILE, JSON.stringify(appointments, null, 2));
  } catch (error) {
    console.error('Error writing appointments:', error);
    throw error;
  }
};

/**
 * Add a new appointment
 * @param {Object} appointmentData - Appointment data
 * @returns {Object} The saved appointment with ID
 */
export const addAppointment = (appointmentData) => {
  try {
    const appointments = readAppointments();

    const newAppointment = {
      id: Date.now().toString(),
      ...appointmentData,
      createdAt: new Date().toISOString(),
      status: 'confirmed'
    };

    appointments.push(newAppointment);
    writeAppointments(appointments);

    return newAppointment;
  } catch (error) {
    console.error('Error adding appointment:', error);
    throw error;
  }
};

/**
 * Get appointments by date range
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Array} Filtered appointments
 */
export const getAppointmentsByDateRange = (startDate, endDate) => {
  try {
    const appointments = readAppointments();

    return appointments.filter(appointment => {
      const appointmentDate = appointment.dataAppuntamento;
      return appointmentDate >= startDate && appointmentDate <= endDate;
    });
  } catch (error) {
    console.error('Error getting appointments by date range:', error);
    return [];
  }
};

/**
 * Get appointments for today
 * @returns {Array} Today's appointments
 */
export const getTodayAppointments = () => {
  const today = new Date().toISOString().split('T')[0];
  return getAppointmentsByDateRange(today, today);
};

/**
 * Update appointment status
 * @param {string} appointmentId - Appointment ID
 * @param {string} status - New status
 * @returns {boolean} Success status
 */
export const updateAppointmentStatus = (appointmentId, status) => {
  try {
    const appointments = readAppointments();
    const appointmentIndex = appointments.findIndex(app => app.id === appointmentId);

    if (appointmentIndex === -1) {
      return false;
    }

    appointments[appointmentIndex].status = status;
    appointments[appointmentIndex].updatedAt = new Date().toISOString();

    writeAppointments(appointments);
    return true;
  } catch (error) {
    console.error('Error updating appointment status:', error);
    return false;
  }
};

/**
 * Delete appointment
 * @param {string} appointmentId - Appointment ID
 * @returns {boolean} Success status
 */
export const deleteAppointment = (appointmentId) => {
  try {
    const appointments = readAppointments();
    const filteredAppointments = appointments.filter(app => app.id !== appointmentId);

    if (filteredAppointments.length === appointments.length) {
      return false; // Appointment not found
    }

    writeAppointments(filteredAppointments);
    return true;
  } catch (error) {
    console.error('Error deleting appointment:', error);
    return false;
  }
};

/**
 * Check if time slot is available
 * @param {string} date - Date (YYYY-MM-DD)
 * @param {string} time - Time (HH:MM)
 * @returns {boolean} Availability status
 */
export const isTimeSlotAvailable = (date, time) => {
  try {
    const appointments = readAppointments();

    return !appointments.some(appointment =>
      appointment.dataAppuntamento === date &&
      appointment.orario === time &&
      appointment.status !== 'cancelled'
    );
  } catch (error) {
    console.error('Error checking time slot availability:', error);
    return false;
  }
};