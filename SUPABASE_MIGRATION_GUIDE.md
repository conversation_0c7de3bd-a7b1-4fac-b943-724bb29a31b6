# CAF Appointment System - Supabase Migration Guide

This guide will help you migrate your CAF appointment booking system from JSON file storage to Supabase.

## 🚀 Quick Start

### Prerequisites
- Supabase account and project
- Node.js installed
- Access to your Supabase project dashboard

### Step 1: Database Setup

1. **Open your Supabase project dashboard**
   - Go to [supabase.com](https://supabase.com)
   - Navigate to your project: `ygfhovbxnpxufgwthbec`

2. **Run the database setup script**
   - Go to SQL Editor in your Supabase dashboard
   - Copy and paste the contents of `supabase-setup.sql`
   - Click "Run" to execute the script

   This will create:
   - `appointments` table with proper schema
   - `admin_users` table for authentication
   - Row Level Security policies
   - Database functions for better performance
   - Indexes for optimal queries

### Step 2: Verify Environment Variables

Ensure your `.env.local` file has the correct Supabase credentials:

```env
SUPABASE_URL=https://ygfhovbxnpxufgwthbec.supabase.co
SUPABASE_API=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.5LRe5nRSLUZl0ilGAGoIk915aUGgT4yoR8xn8pLqY9M

# Keep existing email and admin credentials
EMAIL_USER=<EMAIL>
EMAIL_PASS=jpxjlwdlvfkceqgi
CAF_EMAIL=<EMAIL>
JWT_SECRET=caf-admin-secret-key-2024-montesacro
ADMIN_USERNAME=admin
ADMIN_PASSWORD=caf2024!
```

### Step 3: Install Dependencies

The required dependencies are already installed:
- `@supabase/supabase-js` - Supabase client
- `bcryptjs` - Password hashing

### Step 4: Migrate Existing Data

Run the migration script to transfer your existing appointments from JSON to Supabase:

```bash
npm run migrate
```

This script will:
- ✅ Check Supabase connection
- ✅ Initialize default admin user
- ✅ Create backup of existing JSON file
- ✅ Migrate all appointments to Supabase
- ✅ Verify migration success
- ✅ Provide detailed migration report

### Step 5: Test the System

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Test appointment booking:**
   - Go to `http://localhost:3000`
   - Try booking a new appointment
   - Verify it appears in Supabase dashboard

3. **Test admin panel:**
   - Go to `http://localhost:3000/admin`
   - Login with: `admin` / `caf2024!`
   - Verify appointments are displayed
   - Test status updates and deletion

## 📊 What Changed

### Database Schema

**New `appointments` table:**
```sql
- id (BIGSERIAL PRIMARY KEY)
- nome (VARCHAR)
- cognome (VARCHAR)
- telefono (VARCHAR)
- email (VARCHAR)
- servizio (VARCHAR)
- data_appuntamento (DATE)
- orario (TIME)
- status (VARCHAR)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

**New `admin_users` table:**
```sql
- id (UUID PRIMARY KEY)
- username (VARCHAR)
- email (VARCHAR)
- password_hash (VARCHAR)
- role (VARCHAR)
- is_active (BOOLEAN)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

### API Changes

All API routes now use Supabase instead of JSON files:
- ✅ `/api/admin/appointments` - Uses Supabase queries
- ✅ `/api/admin/login` - Uses Supabase authentication
- ✅ `/api/availability` - Uses Supabase availability checking
- ✅ `/api/send-email` - Saves to Supabase database

### Security Improvements

- ✅ **Row Level Security (RLS)** enabled on all tables
- ✅ **Password hashing** with bcrypt for admin users
- ✅ **Proper authentication** with JWT tokens
- ✅ **Database-level constraints** for data integrity

## 🔧 Configuration

### Supabase Policies

The system uses these RLS policies:

1. **Public appointment booking** - Anyone can create appointments
2. **Public availability checking** - Anyone can check time slot availability
3. **Admin-only management** - Only authenticated admins can view/edit appointments
4. **Secure admin access** - Admin users can only access their own data

### Performance Optimizations

- ✅ Database indexes on frequently queried fields
- ✅ Optimized queries with proper joins
- ✅ Database functions for complex operations
- ✅ Connection pooling through Supabase

## 🚨 Important Notes

### Backward Compatibility

- ✅ **API contracts maintained** - Frontend code requires no changes
- ✅ **Admin credentials preserved** - Existing login still works
- ✅ **Email functionality intact** - All notifications continue working
- ✅ **UI/UX unchanged** - Users see no difference

### Data Safety

- ✅ **Automatic backup** created before migration
- ✅ **Rollback possible** - Can revert to JSON if needed
- ✅ **Data validation** - All migrated data is verified
- ✅ **No data loss** - Migration preserves all appointment details

### Production Deployment

Before deploying to production:

1. **Update environment variables** in your hosting platform
2. **Run migration script** on production data
3. **Test thoroughly** in staging environment
4. **Monitor performance** after deployment

## 🔍 Troubleshooting

### Common Issues

**Migration fails with connection error:**
- Verify SUPABASE_URL and SUPABASE_API in .env.local
- Check Supabase project is active
- Ensure database setup script was run

**Admin login not working:**
- Check JWT_SECRET is set correctly
- Verify admin user was created in database
- Try using environment variable credentials

**Appointments not saving:**
- Check RLS policies are enabled
- Verify table permissions
- Check browser console for errors

### Getting Help

If you encounter issues:
1. Check the browser console for errors
2. Review the migration script output
3. Verify Supabase dashboard shows correct data
4. Test API endpoints individually

## 📈 Next Steps

After successful migration:

1. **Monitor performance** - Check query speeds and response times
2. **Add features** - Consider real-time updates with Supabase subscriptions
3. **Scale up** - Supabase can handle much higher loads than JSON files
4. **Backup strategy** - Set up automated database backups
5. **Analytics** - Use Supabase analytics for insights

## 🎉 Benefits of Migration

- ✅ **Better performance** - Database queries vs file I/O
- ✅ **Real-time capabilities** - Supabase subscriptions for live updates
- ✅ **Scalability** - Handle thousands of appointments
- ✅ **Data integrity** - ACID transactions and constraints
- ✅ **Security** - Row Level Security and proper authentication
- ✅ **Backup & Recovery** - Automated database backups
- ✅ **Analytics** - Built-in query performance monitoring
