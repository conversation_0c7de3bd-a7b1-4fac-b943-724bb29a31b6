// Load environment variables first
import './load-env.js';

import { readAppointments } from '../lib/supabaseAppointmentUtils.js';

console.log('📋 Checking appointments in Supabase...');

try {
  const appointments = await readAppointments();
  
  console.log(`\n📊 Found ${appointments.length} appointments:`);
  
  appointments.forEach((appointment, index) => {
    console.log(`\n${index + 1}. ID: ${appointment.id} (type: ${typeof appointment.id})`);
    console.log(`   Name: ${appointment.nome} ${appointment.cognome}`);
    console.log(`   Date: ${appointment.dataAppuntamento} at ${appointment.orario}`);
    console.log(`   Status: ${appointment.status}`);
    console.log(`   Created: ${appointment.createdAt}`);
  });
  
  if (appointments.length === 0) {
    console.log('\n⚠️  No appointments found. This might mean:');
    console.log('   1. Migration hasn\'t been run yet');
    console.log('   2. No appointments exist in the database');
    console.log('   3. There\'s a connection issue');
  }
  
} catch (error) {
  console.error('❌ Error:', error.message);
}

process.exit(0);
