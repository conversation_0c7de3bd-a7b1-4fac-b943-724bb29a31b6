import { NextResponse } from 'next/server';
import { validateAdminCredentials, generateAdminToken } from '../../../../lib/supabaseAuthUtils.js';

export async function POST(request) {
  try {
    const { username, password } = await request.json();

    // Validate credentials
    const user = await validateAdminCredentials(username, password);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Credenziali non valide' },
        { status: 401 }
      );
    }

    // Generate JWT token
    const token = generateAdminToken(user);

    return NextResponse.json({
      success: true,
      message: 'Login effettuato con successo',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}