/**
 * Data Migration Script: JSON to Supabase
 * This script migrates existing appointment data from JSON files to Supabase
 *
 * Usage: node scripts/migrate-data.js
 */

// Load environment variables first
import './load-env.js';

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { supabase, TABLES, convertJsonToSupabaseFormat } from '../lib/supabase.js';
import { initializeDefaultAdmin } from '../lib/supabaseAuthUtils.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const APPOINTMENTS_FILE = path.join(__dirname, '..', 'data', 'appointments.json');

/**
 * Read appointments from JSON file
 * @returns {Array} Array of appointments
 */
const readJsonAppointments = () => {
  try {
    if (!fs.existsSync(APPOINTMENTS_FILE)) {
      console.log('No appointments.json file found. Nothing to migrate.');
      return [];
    }

    const data = fs.readFileSync(APPOINTMENTS_FILE, 'utf8');
    const appointments = JSON.parse(data);
    
    console.log(`Found ${appointments.length} appointments in JSON file`);
    return appointments;
  } catch (error) {
    console.error('Error reading JSON appointments:', error);
    return [];
  }
};

/**
 * Check if appointment already exists in Supabase
 * @param {Object} appointment - Appointment data
 * @returns {boolean} True if exists, false otherwise
 */
const appointmentExists = async (appointment) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .eq('email', appointment.email)
      .eq('data_appuntamento', appointment.dataAppuntamento)
      .eq('orario', appointment.orario)
      .limit(1);

    if (error) {
      console.error('Error checking appointment existence:', error);
      return false;
    }

    return data.length > 0;
  } catch (error) {
    console.error('Error checking appointment existence:', error);
    return false;
  }
};

/**
 * Migrate a single appointment to Supabase
 * @param {Object} jsonAppointment - Appointment in JSON format
 * @returns {boolean} Success status
 */
const migrateAppointment = async (jsonAppointment) => {
  try {
    // Check if appointment already exists
    if (await appointmentExists(jsonAppointment)) {
      console.log(`Skipping duplicate appointment: ${jsonAppointment.id} (${jsonAppointment.nome} ${jsonAppointment.cognome})`);
      return true;
    }

    // Convert to Supabase format
    const supabaseAppointment = convertJsonToSupabaseFormat(jsonAppointment);
    
    // Remove the id field to let Supabase auto-generate it
    delete supabaseAppointment.id;

    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .insert([supabaseAppointment])
      .select()
      .single();

    if (error) {
      console.error(`Error migrating appointment ${jsonAppointment.id}:`, error);
      return false;
    }

    console.log(`✓ Migrated appointment: ${data.id} (${jsonAppointment.nome} ${jsonAppointment.cognome})`);
    return true;
  } catch (error) {
    console.error(`Error migrating appointment ${jsonAppointment.id}:`, error);
    return false;
  }
};

/**
 * Create backup of JSON file
 */
const createBackup = () => {
  try {
    if (!fs.existsSync(APPOINTMENTS_FILE)) {
      return;
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(path.dirname(APPOINTMENTS_FILE), `appointments-backup-${timestamp}.json`);
    
    fs.copyFileSync(APPOINTMENTS_FILE, backupFile);
    console.log(`✓ Created backup: ${backupFile}`);
  } catch (error) {
    console.error('Error creating backup:', error);
  }
};

/**
 * Verify migration by comparing counts
 */
const verifyMigration = async (originalCount) => {
  try {
    const { count, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('Error verifying migration:', error);
      return false;
    }

    console.log(`\n📊 Migration Summary:`);
    console.log(`   Original JSON appointments: ${originalCount}`);
    console.log(`   Supabase appointments: ${count}`);
    
    if (count >= originalCount) {
      console.log(`✅ Migration verification successful!`);
      return true;
    } else {
      console.log(`❌ Migration verification failed. Some appointments may not have been migrated.`);
      return false;
    }
  } catch (error) {
    console.error('Error verifying migration:', error);
    return false;
  }
};

/**
 * Main migration function
 */
const migrateData = async () => {
  console.log('🚀 Starting data migration from JSON to Supabase...\n');

  try {
    // Initialize default admin user
    console.log('📝 Initializing default admin user...');
    await initializeDefaultAdmin();
    console.log('✓ Admin user initialization complete\n');

    // Read JSON appointments
    console.log('📖 Reading appointments from JSON file...');
    const jsonAppointments = readJsonAppointments();
    
    if (jsonAppointments.length === 0) {
      console.log('No appointments to migrate. Exiting.');
      return;
    }

    // Create backup
    console.log('💾 Creating backup of JSON file...');
    createBackup();

    // Migrate appointments
    console.log(`\n🔄 Migrating ${jsonAppointments.length} appointments...\n`);
    
    let successCount = 0;
    let failureCount = 0;

    for (const appointment of jsonAppointments) {
      const success = await migrateAppointment(appointment);
      if (success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    console.log(`\n📈 Migration Results:`);
    console.log(`   ✅ Successful: ${successCount}`);
    console.log(`   ❌ Failed: ${failureCount}`);

    // Verify migration
    console.log('\n🔍 Verifying migration...');
    const verificationSuccess = await verifyMigration(jsonAppointments.length);

    if (verificationSuccess && failureCount === 0) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('\n📋 Next steps:');
      console.log('   1. Update your API routes to use Supabase');
      console.log('   2. Test the application thoroughly');
      console.log('   3. Update environment variables if needed');
      console.log('   4. Consider removing the JSON file after testing');
    } else {
      console.log('\n⚠️  Migration completed with issues. Please review the errors above.');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
};

/**
 * Check Supabase connection
 */
const checkSupabaseConnection = async () => {
  try {
    console.log('🔗 Checking Supabase connection...');
    
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Supabase connection failed:', error);
      console.log('\n📋 Please ensure:');
      console.log('   1. SUPABASE_URL is set correctly in .env.local');
      console.log('   2. SUPABASE_API key is set correctly in .env.local');
      console.log('   3. Database tables are created (run supabase-setup.sql)');
      console.log('   4. Row Level Security policies are configured');
      return false;
    }

    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Supabase connection error:', error);
    return false;
  }
};

// Run migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    // Check connection first
    const connectionOk = await checkSupabaseConnection();
    if (!connectionOk) {
      console.log('\n❌ Cannot proceed with migration due to connection issues.');
      process.exit(1);
    }

    // Run migration
    await migrateData();
  })();
}

export { migrateData, readJsonAppointments, verifyMigration };
