# Dynamic Two-Level Service Selection Implementation

## Overview
This document describes the implementation of a dynamic two-level service selection system for the CAF appointment booking form. The system allows users to first select a primary service, which then dynamically shows a secondary dropdown with service-specific options (prestazioni).

## Database Changes

### New Column: `prestazione`
A new column has been added to the `appointments` table:

```sql
ALTER TABLE appointments 
ADD COLUMN prestazione VARCHAR(255);
```

**Column Details:**
- **Name**: `prestazione`
- **Type**: `VARCHAR(255)`
- **Nullable**: Yes (optional field)
- **Purpose**: Stores the specific service type selected by the user (e.g., ISEE, Modello 730, etc.)

### Database Migration
Run the following SQL script in your Supabase SQL Editor:

```sql
-- Migration to add prestazione column to appointments table
ALTER TABLE appointments 
ADD COLUMN prestazione VARCHAR(255);

-- Add comment to document the new column
COMMENT ON COLUMN appointments.prestazione IS 'Specific service type selected by the user (e.g., ISEE, Modello 730, etc.)';

-- Optional: Create an index on prestazione for better query performance
CREATE INDEX IF NOT EXISTS idx_appointments_prestazione ON appointments(prestazione);

-- Optional: Create a composite index for service and prestazione queries
CREATE INDEX IF NOT EXISTS idx_appointments_servizio_prestazione ON appointments(servizio, prestazione);
```

## Service Configuration

### Primary Services (Servizi)
The following primary services are available:
1. **Servizi CAF**
2. **Patronato**
3. **Avvocato**
4. **Medico**
5. **Notaio**
6. **Prestiti**
7. **Altri Servizi**

### Service-Prestazione Mapping
Each primary service has specific prestazioni available:

#### Servizi CAF
- ISEE
- IMU
- Modello 730
- Modello Unico PF
- Naspi
- Rinnovo Patente
- Contratti Affitto
- Dimissioni Lavoro Volontarie
- Rinnovo/Aggiornamento Permesso di Soggiorno
- Test Italiano
- Cittadinanza
- Ricongiungimento Familiare

#### Patronato
- Pensione ai Superstiti
- Indennità di Accompagnamento
- Pensione di Invalidità
- Pensione Indiretta

#### Avvocato
- Avvocato per Invalidità
- Avvocato per Lavoro
- Avvocato Problemi Condominiali
- Avvocato Contratti/Cartelle Esattoriali
- Avvocato Penalista

#### Medico
- Medico Mutua
- Medico Certificatore per Invalidità
- Medico Militare

#### Notaio
- Notaio in Sede

#### Prestiti
- AGOS da 3.000€ a 50.000€

#### Altri Servizi
- Polizza Sanitaria
- Polizza Vita
- Polizza Infortuni
- RC Auto

## Implementation Details

### Frontend Changes

#### 1. Form Component (`components/AppointmentForm.js`)
- Added state management for selected service and available prestazioni
- Implemented dynamic dropdown behavior using React hooks
- Added form validation for prestazione field
- Updated analytics tracking to include prestazione information

#### 2. Utilities (`lib/utils.js`)
- Updated services array with new service names
- Added `servicePrestazioni` mapping object

#### 3. Database Layer (`lib/supabase.js`, `lib/supabaseAppointmentUtils.js`)
- Updated data conversion functions to handle prestazione field
- Modified appointment creation logic to include prestazione
- Updated validation functions

#### 4. API Layer (`app/api/send-email/route.js`)
- Updated email templates to include prestazione information
- Modified data extraction to handle prestazione field

### Key Features

#### Dynamic Behavior
1. **Service Selection**: When a user selects a primary service, the prestazione dropdown appears
2. **Prestazione Options**: The prestazione dropdown is populated with service-specific options
3. **Reset on Change**: When the service selection changes, any previously selected prestazione is cleared
4. **Conditional Validation**: Prestazione is only required if the selected service has available prestazioni

#### Form Validation
- Service selection is required
- Prestazione selection is required only if the service has available prestazioni
- Form prevents submission if required fields are missing

#### Analytics Integration
- Service selection is tracked
- Prestazione selection is tracked
- Form completion includes both service and prestazione information

## Usage Instructions

### For Users
1. Select a primary service from the "Servizio" dropdown
2. If the selected service has specific prestazioni, a "Prestazione" dropdown will appear
3. Select the appropriate prestazione from the available options
4. Continue with the rest of the appointment booking process

### For Developers
1. Run the database migration script to add the prestazione column
2. Deploy the updated code
3. Test the form functionality to ensure proper behavior
4. Monitor analytics to track usage patterns

## Testing Checklist

- [ ] Service dropdown shows all 7 primary services
- [ ] Prestazione dropdown appears when a service is selected
- [ ] Prestazione options are correct for each service
- [ ] Prestazione dropdown clears when service changes
- [ ] Form validation works correctly
- [ ] Email notifications include prestazione information
- [ ] Database stores prestazione values correctly
- [ ] Analytics tracking includes prestazione data

## Database Query Examples

### Query appointments with prestazione information
```sql
SELECT nome, cognome, servizio, prestazione, data_appuntamento, orario 
FROM appointments 
WHERE prestazione IS NOT NULL
ORDER BY created_at DESC;
```

### Count appointments by service and prestazione
```sql
SELECT servizio, prestazione, COUNT(*) as count
FROM appointments 
GROUP BY servizio, prestazione
ORDER BY servizio, count DESC;
```

### Find appointments for specific prestazione
```sql
SELECT * FROM appointments 
WHERE prestazione = 'ISEE' 
AND data_appuntamento >= CURRENT_DATE
ORDER BY data_appuntamento, orario;
```

## Notes

- The prestazione field is optional to maintain backward compatibility with existing appointments
- Existing appointments will have NULL values for prestazione
- The system gracefully handles services without prestazioni
- All email notifications now include prestazione information when available
