-- CAF Appointment Booking System - Supabase Database Setup
-- This script creates the necessary tables and policies for the appointment booking system

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'caf-admin-secret-key-2024-mon<PERSON>acro';

-- Create appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id BIGSERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    cognome VARCHAR(100) NOT NULL,
    telefono VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    servizio VARCHAR(255) NOT NULL,
    prestazione VARCHAR(255),
    data_appuntamento DATE NOT NULL,
    orario TIME NOT NULL,
    status VARCHAR(20) DEFAULT 'confirmed' CHECK (status IN ('confirmed', 'completed', 'cancelled', 'no_show')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin_users table for authentication
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appointments_date_time ON appointments(data_appuntamento, orario);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_created_at ON appointments(created_at);
CREATE INDEX IF NOT EXISTS idx_appointments_email ON appointments(email);

-- Enable Row Level Security on tables
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Create policies for appointments table
-- Allow public to insert appointments (for booking)
CREATE POLICY "Allow public to insert appointments" ON appointments
    FOR INSERT TO anon
    WITH CHECK (true);

-- Allow public to read appointments for availability checking
CREATE POLICY "Allow public to read appointments for availability" ON appointments
    FOR SELECT TO anon
    USING (true);

-- Allow authenticated admin users to do everything
CREATE POLICY "Allow admin full access to appointments" ON appointments
    FOR ALL TO authenticated
    USING (true)
    WITH CHECK (true);

-- Create policies for admin_users table
-- Only authenticated users can read admin_users
CREATE POLICY "Allow authenticated users to read admin_users" ON admin_users
    FOR SELECT TO authenticated
    USING (true);

-- Only authenticated users can update their own profile
CREATE POLICY "Allow users to update own profile" ON admin_users
    FOR UPDATE TO authenticated
    USING (auth.uid()::text = id::text)
    WITH CHECK (auth.uid()::text = id::text);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_appointments_updated_at 
    BEFORE UPDATE ON appointments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_users_updated_at 
    BEFORE UPDATE ON admin_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to check appointment availability
CREATE OR REPLACE FUNCTION check_appointment_availability(
    appointment_date DATE,
    appointment_time TIME
)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN NOT EXISTS (
        SELECT 1 FROM appointments 
        WHERE data_appuntamento = appointment_date 
        AND orario = appointment_time 
        AND status != 'cancelled'
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get appointments by date range
CREATE OR REPLACE FUNCTION get_appointments_by_date_range(
    start_date DATE,
    end_date DATE
)
RETURNS TABLE (
    id BIGINT,
    nome VARCHAR,
    cognome VARCHAR,
    telefono VARCHAR,
    email VARCHAR,
    servizio VARCHAR,
    data_appuntamento DATE,
    orario TIME,
    status VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT a.id, a.nome, a.cognome, a.telefono, a.email, a.servizio,
           a.data_appuntamento, a.orario, a.status, a.created_at, a.updated_at
    FROM appointments a
    WHERE a.data_appuntamento >= start_date 
    AND a.data_appuntamento <= end_date
    ORDER BY a.data_appuntamento, a.orario;
END;
$$ LANGUAGE plpgsql;

-- Function to get dashboard statistics
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
    today_date DATE := CURRENT_DATE;
    week_start DATE := today_date - EXTRACT(DOW FROM today_date)::INTEGER + 1;
    week_end DATE := week_start + 6;
    month_start DATE := DATE_TRUNC('month', today_date);
    month_end DATE := (DATE_TRUNC('month', today_date) + INTERVAL '1 month - 1 day')::DATE;
BEGIN
    SELECT json_build_object(
        'total', (SELECT COUNT(*) FROM appointments),
        'today', (SELECT COUNT(*) FROM appointments WHERE data_appuntamento = today_date),
        'thisWeek', (SELECT COUNT(*) FROM appointments WHERE data_appuntamento BETWEEN week_start AND week_end),
        'thisMonth', (SELECT COUNT(*) FROM appointments WHERE data_appuntamento BETWEEN month_start AND month_end),
        'byService', (
            SELECT json_object_agg(servizio, count)
            FROM (
                SELECT servizio, COUNT(*) as count
                FROM appointments
                GROUP BY servizio
            ) service_counts
        ),
        'byStatus', (
            SELECT json_object_agg(status, count)
            FROM (
                SELECT status, COUNT(*) as count
                FROM appointments
                GROUP BY status
            ) status_counts
        ),
        'recent', (
            SELECT json_agg(
                json_build_object(
                    'id', id,
                    'nome', nome,
                    'cognome', cognome,
                    'servizio', servizio,
                    'dataAppuntamento', data_appuntamento,
                    'orario', orario,
                    'status', status
                )
            )
            FROM (
                SELECT id, nome, cognome, servizio, data_appuntamento, orario, status
                FROM appointments
                ORDER BY created_at DESC
                LIMIT 5
            ) recent_appointments
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Insert default admin user (password: caf2024!)
-- Note: In production, this should be done securely
INSERT INTO admin_users (username, email, password_hash, role) 
VALUES (
    'admin', 
    '<EMAIL>', 
    '$2b$10$rQZ9vKzqzqzqzqzqzqzqzOeKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK', -- This is a placeholder - will be handled in the application
    'admin'
) ON CONFLICT (username) DO NOTHING;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON appointments TO anon, authenticated;
GRANT ALL ON admin_users TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
