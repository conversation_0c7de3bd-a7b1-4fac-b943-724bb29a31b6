// Load environment variables first
import './load-env.js';

import { updateAppointmentStatus, deleteAppointment, readAppointments } from '../lib/supabaseAppointmentUtils.js';

console.log('🧪 Testing admin operations...');

try {
  // First, get all appointments
  const appointments = await readAppointments();
  console.log(`\n📊 Found ${appointments.length} appointments`);
  
  if (appointments.length === 0) {
    console.log('❌ No appointments found to test with');
    process.exit(1);
  }

  const testAppointment = appointments[0];
  console.log(`\n🎯 Testing with appointment ID: ${testAppointment.id} (${testAppointment.nome} ${testAppointment.cognome})`);
  console.log(`   Current status: ${testAppointment.status}`);

  // Test status update
  console.log('\n🔄 Testing status update...');
  try {
    const updateResult = await updateAppointmentStatus(testAppointment.id, 'completed');
    console.log(`✅ Status update result: ${updateResult}`);
    
    // Verify the update
    const updatedAppointments = await readAppointments();
    const updatedAppointment = updatedAppointments.find(app => app.id === testAppointment.id);
    console.log(`📋 Updated status: ${updatedAppointment?.status}`);
    
    // Revert the status back
    await updateAppointmentStatus(testAppointment.id, testAppointment.status);
    console.log(`🔄 Reverted status back to: ${testAppointment.status}`);
    
  } catch (error) {
    console.error('❌ Status update failed:', error.message);
  }

  // Test delete (but don't actually delete, just test the validation)
  console.log('\n🗑️  Testing delete validation...');
  try {
    // We won't actually delete, but we can test the ID validation
    if (appointments.length > 1) {
      // Only test if we have multiple appointments
      const lastAppointment = appointments[appointments.length - 1];
      console.log(`🎯 Would test delete with ID: ${lastAppointment.id}`);
      console.log('⚠️  Skipping actual delete to preserve data');
    } else {
      console.log('⚠️  Only one appointment exists, skipping delete test');
    }
  } catch (error) {
    console.error('❌ Delete test failed:', error.message);
  }

} catch (error) {
  console.error('❌ Error:', error.message);
}

process.exit(0);
