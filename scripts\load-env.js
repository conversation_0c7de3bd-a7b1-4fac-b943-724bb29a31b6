/**
 * Environment loader for Node.js scripts
 * This loads environment variables from .env.local file
 */

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local
config({ path: join(__dirname, '..', '.env.local') });

// Verify required environment variables are loaded
const requiredVars = ['SUPABASE_URL', 'SUPABASE_API'];
const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  console.error('📋 Please ensure .env.local file exists and contains:');
  console.error('   SUPABASE_URL=your_supabase_url');
  console.error('   SUPABASE_API=your_supabase_anon_key');
  process.exit(1);
}

console.log('✅ Environment variables loaded successfully');
