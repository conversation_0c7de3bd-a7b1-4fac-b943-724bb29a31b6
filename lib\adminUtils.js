import { readAppointments } from './appointmentUtils.js';

/**
 * Get dashboard statistics
 * @returns {Object} Dashboard stats
 */
export const getDashboardStats = () => {
  try {
    const appointments = readAppointments();
    const today = new Date().toISOString().split('T')[0];
    const thisWeek = getWeekRange(new Date());
    const thisMonth = getMonthRange(new Date());

    const stats = {
      total: appointments.length,
      today: appointments.filter(app => app.dataAppuntamento === today).length,
      thisWeek: appointments.filter(app =>
        app.dataAppuntamento >= thisWeek.start &&
        app.dataAppuntamento <= thisWeek.end
      ).length,
      thisMonth: appointments.filter(app =>
        app.dataAppuntamento >= thisMonth.start &&
        app.dataAppuntamento <= thisMonth.end
      ).length,
      byService: getAppointmentsByService(appointments),
      byStatus: getAppointmentsByStatus(appointments),
      recent: appointments
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5)
    };

    return stats;
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    return {
      total: 0,
      today: 0,
      thisWeek: 0,
      thisMonth: 0,
      byService: {},
      byStatus: {},
      recent: []
    };
  }
};

/**
 * Get appointments grouped by service
 * @param {Array} appointments - Array of appointments
 * @returns {Object} Appointments grouped by service
 */
const getAppointmentsByService = (appointments) => {
  return appointments.reduce((acc, appointment) => {
    const service = appointment.servizio;
    acc[service] = (acc[service] || 0) + 1;
    return acc;
  }, {});
};

/**
 * Get appointments grouped by status
 * @param {Array} appointments - Array of appointments
 * @returns {Object} Appointments grouped by status
 */
const getAppointmentsByStatus = (appointments) => {
  return appointments.reduce((acc, appointment) => {
    const status = appointment.status || 'confirmed';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});
};

/**
 * Get week range for a given date
 * @param {Date} date - Reference date
 * @returns {Object} Week start and end dates
 */
const getWeekRange = (date) => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Monday start
  start.setDate(diff);

  const end = new Date(start);
  end.setDate(start.getDate() + 6);

  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0]
  };
};

/**
 * Get month range for a given date
 * @param {Date} date - Reference date
 * @returns {Object} Month start and end dates
 */
const getMonthRange = (date) => {
  const start = new Date(date.getFullYear(), date.getMonth(), 1);
  const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);

  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0]
  };
};

/**
 * Format appointment data for display
 * @param {Object} appointment - Appointment object
 * @returns {Object} Formatted appointment
 */
export const formatAppointmentForDisplay = (appointment) => {
  return {
    ...appointment,
    formattedDate: formatDate(appointment.dataAppuntamento),
    formattedTime: appointment.orario,
    formattedCreatedAt: formatDateTime(appointment.createdAt),
    statusLabel: getStatusLabel(appointment.status)
  };
};

/**
 * Format date for display
 * @param {string} dateString - Date string (YYYY-MM-DD)
 * @returns {string} Formatted date
 */
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('it-IT', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * Format datetime for display
 * @param {string} dateTimeString - ISO datetime string
 * @returns {string} Formatted datetime
 */
const formatDateTime = (dateTimeString) => {
  const date = new Date(dateTimeString);
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Get status label in Italian
 * @param {string} status - Status code
 * @returns {string} Status label
 */
const getStatusLabel = (status) => {
  const statusLabels = {
    confirmed: 'Confermato',
    completed: 'Completato',
    cancelled: 'Annullato',
    no_show: 'Non presentato'
  };

  return statusLabels[status] || 'Confermato';
};

/**
 * Export appointments to CSV format
 * @param {Array} appointments - Array of appointments
 * @returns {string} CSV content
 */
export const exportAppointmentsToCSV = (appointments) => {
  const headers = [
    'ID',
    'Nome',
    'Cognome',
    'Email',
    'Telefono',
    'Servizio',
    'Data Appuntamento',
    'Orario',
    'Status',
    'Data Creazione'
  ];

  const csvContent = [
    headers.join(','),
    ...appointments.map(app => [
      app.id,
      app.nome,
      app.cognome,
      app.email,
      app.telefono,
      app.servizio,
      app.dataAppuntamento,
      app.orario,
      getStatusLabel(app.status),
      formatDateTime(app.createdAt)
    ].join(','))
  ].join('\n');

  return csvContent;
};