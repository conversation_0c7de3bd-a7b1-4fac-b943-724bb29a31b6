-- Fix RLS policies for CAF Appointment System
-- Run this in your Supabase SQL Editor to fix the admin operations

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Allow admin full access to appointments" ON appointments;

-- Create new policies that allow anon users to perform admin operations
-- This is safe because our API routes already have authentication checks

-- Allow anon users to update appointments (for admin status updates)
CREATE POLICY "Allow anon to update appointments" ON appointments
    FOR UPDATE TO anon
    USING (true)
    WITH CHECK (true);

-- Allow anon users to delete appointments (for admin deletions)
CREATE POLICY "Allow anon to delete appointments" ON appointments
    FOR DELETE TO anon
    USING (true);

-- Keep the existing policies for insert and select
-- These should already exist:
-- - "Allow public to insert appointments" (for booking)
-- - "Allow public to read appointments for availability" (for availability checking)

-- Verify the policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'appointments'
ORDER BY policyname;
