import { NextResponse } from 'next/server';
import { authenticateAdmin } from '../../../../lib/supabaseAuthUtils.js';
import {
  readAppointments,
  updateAppointmentStatus,
  deleteAppointment,
  getAppointmentsByDateRange,
  getDashboardStats
} from '../../../../lib/supabaseAppointmentUtils.js';
import { formatAppointmentForDisplay } from '../../../../lib/adminUtils.js';

// GET - Fetch appointments with optional filters
export async function GET(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const date = searchParams.get('date'); // Single date filter
    const search = searchParams.get('search'); // Search term
    const status = searchParams.get('status'); // Status filter

    if (action === 'stats') {
      // Return dashboard statistics
      const stats = await getDashboardStats();
      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    let appointments;

    if (date) {
      // Get appointments for a specific date
      appointments = await getAppointmentsByDateRange(date, date);
    } else if (startDate && endDate) {
      // Get appointments by date range
      appointments = await getAppointmentsByDateRange(startDate, endDate);
    } else {
      // Get all appointments
      appointments = await readAppointments();
    }

    // Apply search filter
    if (search && search.trim()) {
      const searchTerm = search.toLowerCase().trim();
      appointments = appointments.filter(appointment =>
        appointment.nome.toLowerCase().includes(searchTerm) ||
        appointment.cognome.toLowerCase().includes(searchTerm) ||
        appointment.email.toLowerCase().includes(searchTerm) ||
        appointment.telefono.includes(searchTerm)
      );
    }

    // Apply status filter
    if (status && status !== 'all') {
      appointments = appointments.filter(appointment =>
        (appointment.status || 'confirmed') === status
      );
    }

    // Format appointments for display
    const formattedAppointments = appointments.map(formatAppointmentForDisplay);

    // Sort by date and time (most recent first)
    formattedAppointments.sort((a, b) => {
      const dateA = new Date(`${a.dataAppuntamento} ${a.orario}`);
      const dateB = new Date(`${b.dataAppuntamento} ${b.orario}`);
      return dateB - dateA;
    });

    return NextResponse.json({
      success: true,
      data: formattedAppointments,
      total: formattedAppointments.length
    });

  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}

// PUT - Update appointment status
export async function PUT(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { appointmentId, status } = await request.json();

    if (!appointmentId || !status) {
      return NextResponse.json(
        { success: false, message: 'ID appuntamento e status sono obbligatori' },
        { status: 400 }
      );
    }

    const success = await updateAppointmentStatus(appointmentId, status);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Appuntamento non trovato' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Status aggiornato con successo'
    });

  } catch (error) {
    console.error('Error updating appointment:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}

// DELETE - Delete appointment
export async function DELETE(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const appointmentId = searchParams.get('id');

    if (!appointmentId) {
      return NextResponse.json(
        { success: false, message: 'ID appuntamento obbligatorio' },
        { status: 400 }
      );
    }

    const success = await deleteAppointment(appointmentId);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Appuntamento non trovato' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Appuntamento eliminato con successo'
    });

  } catch (error) {
    console.error('Error deleting appointment:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}