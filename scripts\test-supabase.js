/**
 * Test Script for Supabase Integration
 * This script tests the basic functionality of the Supabase integration
 *
 * Usage: node scripts/test-supabase.js
 */

// Load environment variables first
import './load-env.js';

import { supabase, TABLES } from '../lib/supabase.js';
import { 
  readAppointments, 
  addAppointment, 
  isTimeSlotAvailable,
  getDashboardStats 
} from '../lib/supabaseAppointmentUtils.js';
import { 
  validateAdminCredentials, 
  initializeDefaultAdmin 
} from '../lib/supabaseAuthUtils.js';

/**
 * Test Supabase connection
 */
const testConnection = async () => {
  console.log('🔗 Testing Supabase connection...');
  
  try {
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }

    console.log('✅ Connection successful');
    console.log(`📊 Current appointments in database: ${data || 0}`);
    return true;
  } catch (error) {
    console.error('❌ Connection error:', error.message);
    return false;
  }
};

/**
 * Test admin authentication
 */
const testAdminAuth = async () => {
  console.log('\n🔐 Testing admin authentication...');
  
  try {
    // Initialize default admin
    await initializeDefaultAdmin();
    console.log('✅ Default admin initialized');

    // Test login with environment credentials
    const username = process.env.ADMIN_USERNAME || 'admin';
    const password = process.env.ADMIN_PASSWORD || 'caf2024!';
    
    const user = await validateAdminCredentials(username, password);
    
    if (user) {
      console.log('✅ Admin authentication successful');
      console.log(`👤 User: ${user.username} (${user.role})`);
      return true;
    } else {
      console.log('❌ Admin authentication failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Admin auth error:', error.message);
    return false;
  }
};

/**
 * Test appointment operations
 */
const testAppointmentOperations = async () => {
  console.log('\n📅 Testing appointment operations...');
  
  try {
    // Test reading appointments
    console.log('📖 Testing read appointments...');
    const appointments = await readAppointments();
    console.log(`✅ Read ${appointments.length} appointments`);

    // Test availability checking
    console.log('🕐 Testing availability checking...');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    const isAvailable = await isTimeSlotAvailable(tomorrowStr, '10:00');
    console.log(`✅ Time slot availability check: ${isAvailable ? 'Available' : 'Booked'}`);

    // Test dashboard stats
    console.log('📊 Testing dashboard stats...');
    const stats = await getDashboardStats();
    console.log('✅ Dashboard stats retrieved:');
    console.log(`   Total: ${stats.total}`);
    console.log(`   Today: ${stats.today}`);
    console.log(`   This week: ${stats.thisWeek}`);
    console.log(`   This month: ${stats.thisMonth}`);

    return true;
  } catch (error) {
    console.error('❌ Appointment operations error:', error.message);
    return false;
  }
};

/**
 * Test creating a test appointment (will be cleaned up)
 */
const testAppointmentCreation = async () => {
  console.log('\n➕ Testing appointment creation...');
  
  try {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    // Skip weekends
    if (tomorrow.getDay() === 0 || tomorrow.getDay() === 6) {
      tomorrow.setDate(tomorrow.getDate() + (tomorrow.getDay() === 0 ? 1 : 2));
    }
    
    const testAppointment = {
      nome: 'Test',
      cognome: 'User',
      telefono: '1234567890',
      email: '<EMAIL>',
      servizio: 'Servizi Patronato',
      dataAppuntamento: tomorrow.toISOString().split('T')[0],
      orario: '14:00'
    };

    // Check if slot is available first
    const isAvailable = await isTimeSlotAvailable(testAppointment.dataAppuntamento, testAppointment.orario);
    
    if (!isAvailable) {
      console.log('⚠️  Test slot not available, skipping creation test');
      return true;
    }

    const savedAppointment = await addAppointment(testAppointment);
    console.log(`✅ Test appointment created with ID: ${savedAppointment.id}`);

    // Clean up - delete the test appointment
    const { error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .delete()
      .eq('id', savedAppointment.id);

    if (error) {
      console.log(`⚠️  Could not clean up test appointment: ${error.message}`);
    } else {
      console.log('🧹 Test appointment cleaned up');
    }

    return true;
  } catch (error) {
    console.error('❌ Appointment creation error:', error.message);
    return false;
  }
};

/**
 * Test database schema
 */
const testDatabaseSchema = async () => {
  console.log('\n🗄️  Testing database schema...');
  
  try {
    // Test appointments table structure
    const { data: appointmentsSchema, error: appointmentsError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .limit(1);

    if (appointmentsError && appointmentsError.code !== 'PGRST116') {
      console.error('❌ Appointments table error:', appointmentsError.message);
      return false;
    }

    console.log('✅ Appointments table accessible');

    // Test admin_users table structure
    const { data: adminSchema, error: adminError } = await supabase
      .from(TABLES.ADMIN_USERS)
      .select('id, username, email, role, is_active')
      .limit(1);

    if (adminError && adminError.code !== 'PGRST116') {
      console.error('❌ Admin users table error:', adminError.message);
      return false;
    }

    console.log('✅ Admin users table accessible');
    return true;
  } catch (error) {
    console.error('❌ Database schema error:', error.message);
    return false;
  }
};

/**
 * Main test function
 */
const runTests = async () => {
  console.log('🧪 Starting Supabase Integration Tests\n');
  
  const tests = [
    { name: 'Connection', fn: testConnection },
    { name: 'Database Schema', fn: testDatabaseSchema },
    { name: 'Admin Authentication', fn: testAdminAuth },
    { name: 'Appointment Operations', fn: testAppointmentOperations },
    { name: 'Appointment Creation', fn: testAppointmentCreation }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" threw an error:`, error.message);
      failed++;
    }
  }

  console.log('\n📋 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! Your Supabase integration is working correctly.');
    console.log('\n📋 Next steps:');
    console.log('   1. Run the migration script: npm run migrate');
    console.log('   2. Start the development server: npm run dev');
    console.log('   3. Test the application in your browser');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the errors above and:');
    console.log('   1. Verify your .env.local file has correct Supabase credentials');
    console.log('   2. Ensure the database setup script (supabase-setup.sql) was run');
    console.log('   3. Check your Supabase project is active and accessible');
  }
};

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { runTests };
