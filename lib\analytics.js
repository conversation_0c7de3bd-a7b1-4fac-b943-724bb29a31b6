// Google Analytics 4 Event Tracking Utilities

/**
 * Send a custom event to Google Analytics
 * @param {string} eventName - The name of the event
 * @param {Object} parameters - Event parameters
 */
export const trackEvent = (eventName, parameters = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      ...parameters,
      // Add timestamp for better tracking
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Track form field interactions
 * @param {string} fieldName - Name of the form field
 * @param {string} fieldType - Type of the field (select, input, etc.)
 * @param {string} value - Value selected/entered (optional)
 */
export const trackFormFieldInteraction = (fieldName, fieldType, value = null) => {
  trackEvent('form_field_interaction', {
    field_name: fieldName,
    field_type: fieldType,
    field_value: value,
    form_name: 'appointment_booking',
  });
};

/**
 * Track form submission attempts
 * @param {string} status - 'started', 'success', 'error'
 * @param {Object} formData - Form data (sanitized)
 * @param {string} errorMessage - Error message if status is 'error'
 */
export const trackFormSubmission = (status, formData = {}, errorMessage = null) => {
  const eventData = {
    form_name: 'appointment_booking',
    submission_status: status,
  };

  // Add sanitized form data for successful submissions
  if (status === 'success' && formData) {
    eventData.service_type = formData.servizio;
    eventData.appointment_date = formData.dataAppuntamento;
    eventData.appointment_time = formData.orario;
    // Don't track personal information like name, email, phone
  }

  // Add error information
  if (status === 'error' && errorMessage) {
    eventData.error_message = errorMessage;
  }

  trackEvent('form_submission', eventData);
};

/**
 * Track appointment booking funnel steps
 * @param {string} step - 'form_viewed', 'date_selected', 'time_selected', 'form_completed'
 * @param {Object} additionalData - Additional tracking data
 */
export const trackBookingFunnel = (step, additionalData = {}) => {
  trackEvent('booking_funnel', {
    funnel_step: step,
    ...additionalData,
  });
};

/**
 * Track availability checks
 * @param {string} date - Selected date
 * @param {number} availableSlots - Number of available slots
 * @param {number} totalSlots - Total number of slots
 */
export const trackAvailabilityCheck = (date, availableSlots, totalSlots) => {
  trackEvent('availability_check', {
    selected_date: date,
    available_slots: availableSlots,
    total_slots: totalSlots,
    availability_rate: totalSlots > 0 ? (availableSlots / totalSlots) : 0,
  });
};

/**
 * Track page views manually (for SPA navigation)
 * @param {string} pagePath - Path of the page
 * @param {string} pageTitle - Title of the page
 */
export const trackPageView = (pagePath, pageTitle) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
      page_path: pagePath,
      page_title: pageTitle,
    });
  }
};

/**
 * Track user engagement events
 * @param {string} action - The engagement action
 * @param {Object} parameters - Additional parameters
 */
export const trackEngagement = (action, parameters = {}) => {
  trackEvent('engagement', {
    engagement_action: action,
    ...parameters,
  });
};

/**
 * Track errors and exceptions
 * @param {string} description - Error description
 * @param {boolean} fatal - Whether the error is fatal
 * @param {string} location - Where the error occurred
 */
export const trackError = (description, fatal = false, location = '') => {
  trackEvent('exception', {
    description,
    fatal,
    location,
  });
};
